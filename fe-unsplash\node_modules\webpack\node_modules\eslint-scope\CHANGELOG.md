v5.1.1 - September 12, 2020

* [`9b528d7`](https://github.com/eslint/eslint-scope/commit/9b528d778c381718c12dabfb7f1c0e0dc6b36e49) Upgrade: esrecurse version to ^4.3.0 (#64) (<PERSON><PERSON><PERSON>)
* [`f758bbc`](https://github.com/eslint/eslint-scope/commit/f758bbc3d49b9b9ea2289a5d6a6bba8dcf2c4903) Chore: fix definiton -> definition typo in comments (#63) (<PERSON>)
* [`7513734`](https://github.com/eslint/eslint-scope/commit/751373473375b3f2edc4eaf1c8d2763d8435bb72) Chore: move to GitHub Actions (#62) (<PERSON>)

v5.1.0 - June 4, 2020

* [`d4a3764`](https://github.com/eslint/eslint-scope/commit/d4a376434b16289c1a428d7e304576e997520873) Update: support new export syntax (#56) (<PERSON><PERSON>)

v5.0.0 - July 20, 2019

* [`e9fa22e`](https://github.com/eslint/eslint-scope/commit/e9fa22ea412c26cf2761fa98af7e715644bdb464) Upgrade: update dependencies after dropping support for Node <8 (#53) (Kai Cataldo)
* [`ee9f7c1`](https://github.com/eslint/eslint-scope/commit/ee9f7c12721aa195ba7e0e69551f49bfdb479951) Breaking: drop support for Node v6 (#54) (Kai Cataldo)

v4.0.3 - March 15, 2019

* [`299df64`](https://github.com/eslint/eslint-scope/commit/299df64bdafb30b4d9372e4b7af0cf51a3818c4a) Fix: arrow function scope strictness (take 2) (#52) (futpib)

v4.0.2 - March 1, 2019

* [`c925600`](https://github.com/eslint/eslint-scope/commit/c925600a684ae0f71b96f85339437a43b4d50d99) Revert "Fix: Arrow function scope strictness (fixes #49) (#50)" (#51) (Teddy Katz)

v4.0.1 - March 1, 2019

* [`2533966`](https://github.com/eslint/eslint-scope/commit/2533966faf317df5a3847fab937ba462c16808b8) Fix: Arrow function scope strictness (fixes #49) (#50) (futpib)
* [`0cbeea5`](https://github.com/eslint/eslint-scope/commit/0cbeea51dfb66ab88ea34b0e3b4ad5e6cc210f2f) Chore: add supported Node.js versions to CI (#47) (Kai Cataldo)
* [`b423057`](https://github.com/eslint/eslint-scope/commit/b42305760638b8edf4667acf1445e450869bd983) Upgrade: eslint-release@1.0.0 (#46) (Teddy Katz)

v4.0.0 - June 21, 2018



v4.0.0-rc.0 - June 9, 2018

* 3b919b8 Build: Adding rc release script to package.json (#38) (Kevin Partington)
* 137732a Chore: avoid creating package-lock.json files (#37) (Teddy Katz)

v4.0.0-alpha.0 - April 27, 2018

* 7cc3769 Upgrade: eslint-release ^0.11.1 (#36) (Teddy Katz)
* c9f6967 Breaking: remove TDZScope (refs eslint/eslint#10245) (#35) (Toru Nagashima)
* 982a71f Fix: wrong resolution about default parameters (#33) (Toru Nagashima)
* 57889f1 Docs: Remove extra header line from LICENSE (#32) (Gyandeep Singh)

v3.7.1 - April 12, 2017

* ced6262 Fix: restore previous Scope API exports from escope (#31) (Vitor Balocco)
* 5c3d966 Fix: Remove and Modify tests that contain invalid ES6 syntax (#29) (Reyad Attiyat)

v3.7.0 - March 17, 2017

* 9e27835 Chore: Add files section to package.json (#24) (Ilya Volodin)
* 3e4d123 Upgrade: eslint-config-eslint to 4.0.0 (#21) (Teddy Katz)
* 38c50fb Chore: Rename src to lib and test to tests (#20) (Corbin Uselton)
* f4cd920 Chore: Remove esprima (#19) (Corbin Uselton)
* f81fad5 Revert "Chore: Remove esprima" (#18) (James Henry)
* 31b0085 Chore: Remove es6-map and es6-weakmap as they are included in node4 (#10) (#13) (Corbin Uselton)
* 12a1ca1 Add Makefile.js and eslint (#15) (Reyad Attiyat)
* 7d23f8e Chore: Remove es6-map and es6-weakmap as they are included in node4 (#10) (Corbin Uselton)
* 019441e Chore: Convert to ES6 that is supported on Node 4, commonjs modules and remove Babel (#14) (Corbin Uselton)
* c647f65 Update: Add check for node.body in referencer (#2) (Corbin Uselton)
* eb5c9db Remove browserify and jsdoc (#12) (Corbin Uselton)
* cf38df0 Chore: Update README.md (#3) (James Henry)
* 8a142ca Chore: Add eslint-release scripts (#6) (James Henry)
* e60d8cb Chore: Remove unused bower.json (#5) (James Henry)
* 049c545 Chore: Fix tests for eslint-scope (#4) (James Henry)
* f026aab Chore: Update package.json for eslint fork (#1) (James Henry)
* a94d281 Chore: Update license with JSF copyright (Nicholas C. Zakas)

